/**
 * Testes para utilitários de validação
 */

import {
  validateEmail,
  validateUrl,
  sanitizeString,
  validateUserProgress,
  validateStepId,
  validateForm,
  escapeHtml,
  isLocalStorageAvailable
} from '../validation';

describe('Validation Utils', () => {
  describe('validateEmail', () => {
    it('should validate correct emails', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    it('should reject invalid emails', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'user@',
        'user@.com',
        '<EMAIL>',
        '',
        'user <EMAIL>'
      ];

      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });

    it('should handle whitespace', () => {
      expect(validateEmail('  <EMAIL>  ')).toBe(true);
    });
  });

  describe('validateUrl', () => {
    it('should validate correct URLs', () => {
      const validUrls = [
        'https://example.com',
        'http://test.org',
        'https://sub.domain.com/path?query=1',
        'ftp://files.example.com'
      ];

      validUrls.forEach(url => {
        expect(validateUrl(url)).toBe(true);
      });
    });

    it('should reject invalid URLs', () => {
      const invalidUrls = [
        'not-a-url',
        'http://',
        'https://.com',
        '',
        'just-text'
      ];

      invalidUrls.forEach(url => {
        expect(validateUrl(url)).toBe(false);
      });
    });
  });

  describe('sanitizeString', () => {
    it('should remove dangerous characters', () => {
      const input = '<script>alert("xss")</script>';
      const result = sanitizeString(input);
      expect(result).not.toContain('<');
      expect(result).not.toContain('>');
    });

    it('should trim whitespace', () => {
      const input = '  test string  ';
      const result = sanitizeString(input);
      expect(result).toBe('test string');
    });

    it('should limit string length', () => {
      const longString = 'a'.repeat(2000);
      const result = sanitizeString(longString);
      expect(result.length).toBeLessThanOrEqual(1000);
    });
  });

  describe('validateUserProgress', () => {
    it('should validate correct progress object', () => {
      const validProgress = {
        currentStep: 1,
        completedSteps: [0],
        affiliateLink: 'test-link',
        isVerified: false
      };

      expect(validateUserProgress(validProgress)).toBe(true);
    });

    it('should reject invalid progress objects', () => {
      const invalidProgresses = [
        null,
        undefined,
        {},
        { currentStep: 1 }, // missing fields
        'not-an-object'
      ];

      invalidProgresses.forEach(progress => {
        expect(validateUserProgress(progress)).toBe(false);
      });
    });
  });

  describe('validateStepId', () => {
    it('should validate correct step IDs', () => {
      const validStepIds = [0, 1, 2, 3, 4];
      
      validStepIds.forEach(stepId => {
        expect(validateStepId(stepId)).toBe(true);
      });
    });

    it('should reject invalid step IDs', () => {
      const invalidStepIds = [-1, 5, 10, 'not-a-number', null, undefined];
      
      invalidStepIds.forEach(stepId => {
        expect(validateStepId(stepId)).toBe(false);
      });
    });
  });

  describe('validateForm', () => {
    it('should validate form with valid email', () => {
      const formData = {
        email: '<EMAIL>'
      };

      const result = validateForm(formData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject form with invalid email', () => {
      const formData = {
        email: 'invalid-email'
      };

      const result = validateForm(formData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Email inválido');
    });

    it('should handle empty form data', () => {
      const result = validateForm({});
      expect(result.isValid).toBe(true); // No required fields in current implementation
    });
  });

  describe('escapeHtml', () => {
    it('should escape HTML characters', () => {
      const input = '<script>alert("xss")</script>';
      const result = escapeHtml(input);
      expect(result).toContain('&lt;');
      expect(result).toContain('&gt;');
    });

    it('should handle normal text', () => {
      const input = 'Normal text without HTML';
      const result = escapeHtml(input);
      expect(result).toBe(input);
    });
  });

  describe('isLocalStorageAvailable', () => {
    it('should detect localStorage availability', () => {
      // This test depends on the test environment
      const result = isLocalStorageAvailable();
      expect(typeof result).toBe('boolean');
    });
  });
});
