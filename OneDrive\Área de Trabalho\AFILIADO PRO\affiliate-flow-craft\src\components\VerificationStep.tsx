
import React from 'react';
import FeedbackMessage from './FeedbackMessage';
import { Shield, CheckCircle, DollarSign, Users } from 'lucide-react';

interface VerificationStepProps {
  isLoading: boolean;
  feedbackMessage: string;
  onVerify: () => void;
}

const VerificationStep: React.FC<VerificationStepProps> = ({ 
  isLoading, 
  feedbackMessage, 
  onVerify 
}) => {
  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 animate-slide-up">
      <h2 className="text-3xl font-bold text-lp-light mb-4">
        🔐 Verificar Seu Cadastro GRIP
      </h2>
      <p className="text-white/80 mb-6">
        Vamos verificar se seu cadastro no GRIP foi realizado com sucesso e ativar 
        seu acesso completo ao sistema de comissões e ferramentas de afiliado.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        <div className="bg-white/5 backdrop-blur-sm p-4 rounded-lg border border-white/10 flex items-start gap-3">
          <div className="mt-1">
            <CheckCircle className="w-5 h-5 text-green-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-lp-light mb-1">Sistema de Ranks</h3>
            <p className="text-white/70 text-sm">Evolua através dos Ranks GRIP e aumente suas comissões. Quanto mais indicações, maiores seus ganhos.</p>
          </div>
        </div>
        
        <div className="bg-white/5 backdrop-blur-sm p-4 rounded-lg border border-white/10 flex items-start gap-3">
          <div className="mt-1">
            <DollarSign className="w-5 h-5 text-green-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-lp-light mb-1">MaxCom Exclusivo</h3>
            <p className="text-white/70 text-sm">Recurso que recompensa seu esforço em ajudar clientes a fazer upgrade para planos maiores.</p>
          </div>
        </div>
        
        <div className="bg-white/5 backdrop-blur-sm p-4 rounded-lg border border-white/10 flex items-start gap-3">
          <div className="mt-1">
            <Users className="w-5 h-5 text-blue-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-lp-light mb-1">Leads Orgânicos</h3>
            <p className="text-white/70 text-sm">Novos membros GRIP de campanhas da Gaio serão automaticamente vinculados aos membros mais engajados.</p>
          </div>
        </div>
        
        <div className="bg-white/5 backdrop-blur-sm p-4 rounded-lg border border-white/10 flex items-start gap-3">
          <div className="mt-1">
            <Shield className="w-5 h-5 text-blue-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-lp-light mb-1">Rastreamento Inteligente</h3>
            <p className="text-white/70 text-sm">Nossa tecnologia avançada garante que sua afiliação seja mantida, mesmo se os usuários acessarem o site da Gaio diretamente.</p>
          </div>
        </div>
      </div>
      
      <button
        onClick={onVerify}
        disabled={isLoading}
        className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-lg font-bold rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed w-full md:w-auto"
      >
        {isLoading ? '🔄 Verificando...' : '🚀 Verificar Meu Cadastro'}
      </button>

      {feedbackMessage && (
        <div className="mt-6">
          <FeedbackMessage message={feedbackMessage} type="success" />
        </div>
      )}
    </div>
  );
};

export default VerificationStep;
