import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  text, 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  return (
    <div className={`flex items-center justify-center gap-3 ${className}`}>
      <Loader2 
        className={`${sizeClasses[size]} animate-spin text-lp-blue`}
        aria-hidden="true"
      />
      {text && (
        <span className={`${textSizeClasses[size]} text-lp-light/80 font-medium`}>
          {text}
        </span>
      )}
    </div>
  );
};

// Skeleton loader para melhor UX
export const SkeletonLoader: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`animate-pulse bg-lp-gray/30 rounded ${className}`} />
);

// Loading overlay para tela cheia
export const LoadingOverlay: React.FC<{ isVisible: boolean; text?: string }> = ({ 
  isVisible, 
  text = 'Carregando...' 
}) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-lp-navy/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="glass-dark rounded-2xl p-8 text-center">
        <LoadingSpinner size="lg" text={text} />
      </div>
    </div>
  );
};

export default LoadingSpinner;
