
import React from 'react';

const WelcomeStep: React.FC = () => {
  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 animate-slide-up">
      <h2 className="text-3xl font-bold text-lp-light mb-4">
        ✅ Bem-vindo ao GRIP!
      </h2>
      <p className="text-white/80 mb-6">
        Parabéns por dar o primeiro passo para transformar sua rede de contatos em uma fonte de renda recorrente!
        O GRIP (Gaio Referral & Impact Program) oferece uma oportunidade única de ganhos.
      </p>
      
      <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 mb-6">
        <h3 className="text-blue-300 font-bold mb-2">Como funciona o sistema de comissões:</h3>
        <ul className="text-blue-100/80 space-y-2">
          <li className="flex items-start gap-2">
            <span className="text-blue-300 font-bold">•</span>
            <span><strong>Indicação de Empresas:</strong> Ganhe 8% a 12% de comissões recorrentes em planos contratados.</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-blue-300 font-bold">•</span>
            <span><strong>Convite de Membros:</strong> Ganhe 1% a 3% de comissões em clientes indicados pela sua rede.</span>
          </li>
        </ul>
      </div>
      
      <div className="bg-lp-success/20 border border-green-500/30 rounded-lg p-4 mb-6">
        <p className="text-green-300 text-sm">
          💡 <strong>Dica:</strong> Com o MaxCom, seu nível de comissão é determinado pelo seu cliente de maior valor. 
          Se um cliente se tornar Enterprise, você recebe as taxas máximas em TODOS os seus clientes!
        </p>
      </div>
    </div>
  );
};

export default WelcomeStep;
