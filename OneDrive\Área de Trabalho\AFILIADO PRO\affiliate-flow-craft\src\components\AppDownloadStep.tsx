
import React from 'react';
import { AppDownloadLinks } from '../types/landing';
import FeedbackMessage from './FeedbackMessage';
import { Smartphone, Apple, BarChart3, Clock } from 'lucide-react';

interface AppDownloadStepProps {
  isLoading: boolean;
  feedbackMessage: string;
  onDownload: (platform: 'android' | 'ios') => void;
}

const AppDownloadStep: React.FC<AppDownloadStepProps> = ({ 
  isLoading, 
  feedbackMessage, 
  onDownload 
}) => {
  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 animate-slide-up">
      <h2 className="text-3xl font-bold text-lp-light mb-4">
        📱 Baixe o App GRIP
      </h2>
      <p className="text-white/80 mb-6">
        Acompanhe suas comissões em tempo real e gerencie seus indicados através do aplicativo oficial GRIP. 
        Escolha sua plataforma preferida para download:
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all">
          <h3 className="text-xl font-bold text-lp-light mb-4 flex items-center">
            <BarChart3 className="w-5 h-5 mr-2 text-green-400" />
            Acompanhamento em Tempo Real
          </h3>
          <p className="text-white/70 mb-4">Com o app GRIP, você pode monitorar suas comissões e o status de seus indicados em tempo real, sem precisar acessar o computador.</p>
        </div>
        
        <div className="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all">
          <h3 className="text-xl font-bold text-lp-light mb-4 flex items-center">
            <Clock className="w-5 h-5 mr-2 text-blue-400" />
            Pagamentos Transparentes
          </h3>
          <p className="text-white/70 mb-4">Acompanhe o fluxo de pagamento de forma transparente e eficiente. Os pagamentos são processados todo dia 5 do mês seguinte via Pix.</p>
        </div>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <button
          onClick={() => onDownload('android')}
          disabled={isLoading}
          className="flex items-center justify-center px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all shadow-md hover:shadow-lg disabled:opacity-50"
        >
          <Smartphone className="w-5 h-5 mr-2" />
          Baixar para Android
        </button>
        
        <button
          onClick={() => onDownload('ios')}
          disabled={isLoading}
          className="flex items-center justify-center px-6 py-3 bg-gradient-to-r from-gray-700 to-gray-800 text-white rounded-lg hover:from-gray-800 hover:to-gray-900 transition-all shadow-md hover:shadow-lg disabled:opacity-50"
        >
          <Apple className="w-5 h-5 mr-2" />
          Baixar para iOS
        </button>
      </div>

      {feedbackMessage && (
        <FeedbackMessage message={feedbackMessage} />
      )}
      
      <div className="mt-6 bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <p className="text-blue-300 text-sm">
          💡 <strong>Dica:</strong> O aplicativo GRIP facilita o compartilhamento de seus links de afiliado 
          diretamente para suas redes sociais e contatos, aumentando suas chances de conversão.
        </p>
      </div>
    </div>
  );
};

export default AppDownloadStep;
