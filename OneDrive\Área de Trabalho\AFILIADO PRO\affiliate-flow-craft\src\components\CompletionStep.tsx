
import React from 'react';
import { UserProgress } from '../types/landing';
import FeedbackMessage from './FeedbackMessage';
import { Mail, Copy, ExternalLink, DollarSign, BarChart3, Users, Repeat } from 'lucide-react';

interface CompletionStepProps {
  progress: UserProgress;
  isLoading: boolean;
  feedbackMessage: string;
  onEmailSend: () => void;
  onCopyLink: () => void;
}

const CompletionStep: React.FC<CompletionStepProps> = ({ 
  progress, 
  isLoading, 
  feedbackMessage, 
  onEmailSend, 
  onCopyLink 
}) => {
  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 animate-slide-up">
      <div className="flex justify-center mb-6">
        <div className="w-20 h-20 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
          <DollarSign className="w-10 h-10 text-white" />
        </div>
      </div>
      
      <h2 className="text-3xl font-bold text-lp-light mb-4 text-center">
        🎉 Parabéns! Você está pronto para começar a ganhar com o GRIP!
      </h2>
      <p className="text-white/80 mb-8 text-center">
        Seu cadastro no GRIP foi concluído com sucesso! Agora você tem acesso a todas as ferramentas 
        e recursos para transformar sua rede de contatos em uma fonte de renda recorrente.
      </p>

      <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-lg p-6 mb-8">
        <h3 className="text-xl font-bold text-lp-light mb-4 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2 text-blue-400" />
          Próximos Passos para Maximizar seus Ganhos:
        </h3>
        <ul className="text-white/90 space-y-3">
          <li className="flex items-start gap-2">
            <span className="text-green-400 font-bold">1.</span>
            <span><strong>Compartilhe seu Link:</strong> Envie seu link de afiliado para empresas que podem se beneficiar do Gaio DataOS.</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-green-400 font-bold">2.</span>
            <span><strong>Convide Membros:</strong> Expanda sua rede convidando pessoas para o programa GRIP e aumente suas possibilidades de ganhos.</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-green-400 font-bold">3.</span>
            <span><strong>Acompanhe no App:</strong> Use o aplicativo GRIP para monitorar suas comissões e o status de seus indicados em tempo real.</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-green-400 font-bold">4.</span>
            <span><strong>Evolua nos Ranks:</strong> Quanto mais indicações você fizer, maiores serão suas comissões através do sistema de ranks.</span>
          </li>
        </ul>
      </div>

      <div className="bg-gradient-to-r from-purple-500/20 to-orange-500/20 border border-purple-500/30 rounded-lg p-6 mb-8">
        <h3 className="text-xl font-bold text-lp-light mb-4 flex items-center">
          <Repeat className="w-5 h-5 mr-2 text-purple-400" />
          Benefícios do Programa GRIP:
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white/10 p-4 rounded-lg">
            <h4 className="font-bold text-lp-light mb-2">Ganhos Recorrentes</h4>
            <p className="text-white/80 text-sm">Receba comissões mensais enquanto seus indicados continuarem usando o Gaio DataOS.</p>
          </div>
          <div className="bg-white/10 p-4 rounded-lg">
            <h4 className="font-bold text-lp-light mb-2">Oportunidade Global</h4>
            <p className="text-white/80 text-sm">Indique empresas e pessoas de qualquer lugar do mundo e amplie seu potencial de ganhos.</p>
          </div>
          <div className="bg-white/10 p-4 rounded-lg">
            <h4 className="font-bold text-lp-light mb-2">Sistema MaxCom</h4>
            <p className="text-white/80 text-sm">Tecnologia exclusiva que maximiza suas comissões através de um sistema inteligente de distribuição.</p>
          </div>
          <div className="bg-white/10 p-4 rounded-lg">
            <h4 className="font-bold text-lp-light mb-2">Impacto Social</h4>
            <p className="text-white/80 text-sm">Parte das comissões é destinada a projetos sociais, gerando impacto positivo na sociedade.</p>
          </div>
        </div>
      </div>

      <div className="flex flex-col space-y-4 mt-6">
        <button 
          onClick={onEmailSend}
          disabled={isLoading}
          className="bg-gradient-to-r from-blue-600 to-blue-800 hover:from-blue-700 hover:to-blue-900 text-white font-bold py-3 px-6 rounded-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-lg shadow-blue-700/30"
        >
          <Mail className="w-5 h-5 mr-2" /> Receber Dicas Exclusivas por Email
        </button>
        
        <button 
          onClick={onCopyLink}
          disabled={isLoading}
          className="bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-700 hover:to-purple-900 text-white font-bold py-3 px-6 rounded-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-lg shadow-purple-700/30"
        >
          <Copy className="w-5 h-5 mr-2" /> Copiar Meu Link de Afiliado GRIP
        </button>

        <a 
          href="https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368" 
          target="_blank" 
          rel="noopener noreferrer"
          className="bg-gradient-to-r from-green-600 to-green-800 hover:from-green-700 hover:to-green-900 text-white font-bold py-3 px-6 rounded-lg transition-all flex items-center justify-center shadow-lg shadow-green-700/30 mt-2"
        >
          <ExternalLink className="w-5 h-5 mr-2" /> Acessar Minha Conta GRIP
        </a>
      </div>

      {progress.affiliateLink && (
        <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-4 mb-4">
          <p className="text-xs text-white/60 mb-1">Seu link de afiliado:</p>
          <p className="text-white font-mono text-sm break-all">{progress.affiliateLink}</p>
        </div>
      )}

      {feedbackMessage && (
        <div className="mt-4">
          <FeedbackMessage message={feedbackMessage} type="success" />
        </div>
      )}

      <p className="text-white/60 text-sm text-center mt-8">
        GRIP - Gaio Referral & Impact Program | Transforme sua rede em renda recorrente
      </p>
    </div>
  );
};

export default CompletionStep;
