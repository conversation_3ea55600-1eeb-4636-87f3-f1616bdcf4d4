import { useState, useEffect, useCallback } from 'react';
import LandingStateManager from '../utils/landingState';
import { UserProgress, StepState } from '../types/landing';
import { validateEmail, validateStepId, rateLimiter, createSession, validateSession } from '../utils/validation';
import { trackStepCompleted, trackError, trackEvent } from '../utils/analytics';

interface UseLandingStateReturn {
  progress: UserProgress;
  steps: StepState[];
  isLoading: boolean;
  error: string | null;
  completeStep: (stepId: number) => Promise<void>;
  setAffiliateLink: (link: string) => void;
  setEmail: (email: string) => void;
  setVerified: (verified: boolean) => void;
  reset: () => void;
  clearError: () => void;
}

export const useLandingState = (): UseLandingStateReturn => {
  const [progress, setProgress] = useState<UserProgress>({
    currentStep: 0,
    completedSteps: [],
    affiliateLink: '',
    isVerified: false
  });
  const [steps, setSteps] = useState<StepState[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const stateManager = LandingStateManager.getInstance();

  useEffect(() => {
    try {
      // Valida sessão
      if (!validateSession()) {
        createSession();
      }

      // Inicializa o estado
      setProgress(stateManager.getProgress());
      setSteps(stateManager.getSteps());

      // Subscreve para updates
      const unsubscribe = stateManager.subscribe((newProgress) => {
        setProgress(newProgress);
        setSteps(stateManager.getSteps());
      });

      return unsubscribe;
    } catch (err) {
      setError('Erro ao inicializar estado da landing page');
      console.error('Erro no useLandingState:', err);
    }
  }, [stateManager]);

  const completeStep = useCallback(async (stepId: number) => {
    setIsLoading(true);
    setError(null);

    try {
      // Validação de entrada
      if (!validateStepId(stepId)) {
        throw new Error('ID da etapa inválido');
      }

      // Rate limiting
      const rateLimitKey = `step_${stepId}`;
      if (!rateLimiter.isAllowed(rateLimitKey, 3, 10000)) {
        throw new Error('Muitas tentativas. Aguarde alguns segundos.');
      }

      // Simula delay para melhor UX
      await new Promise(resolve => setTimeout(resolve, 500));
      stateManager.completeStep(stepId);

      // Track analytics
      const stepNames = ['Início', 'Link Gerado', 'App Baixado', 'Acesso Verificado', 'Material Liberado'];
      trackStepCompleted(stepId, stepNames[stepId] || `Etapa ${stepId}`);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `Erro ao completar etapa ${stepId + 1}`;
      setError(errorMessage);
      trackError('step_completion_error', errorMessage);
      console.error('Erro ao completar etapa:', err);
    } finally {
      setIsLoading(false);
    }
  }, [stateManager]);

  const setAffiliateLink = useCallback((link: string) => {
    try {
      stateManager.setAffiliateLink(link);
    } catch (err) {
      setError('Erro ao definir link de afiliado');
      console.error('Erro ao definir link:', err);
    }
  }, [stateManager]);

  const setEmail = useCallback((email: string) => {
    try {
      // Validação de email melhorada
      if (!validateEmail(email)) {
        throw new Error('Email inválido');
      }

      // Rate limiting para email
      if (!rateLimiter.isAllowed('email_update', 5, 60000)) {
        throw new Error('Muitas tentativas de atualização de email. Aguarde um minuto.');
      }

      stateManager.setEmail(email.trim().toLowerCase());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Email inválido';
      setError(errorMessage);
      console.error('Erro ao definir email:', err);
    }
  }, [stateManager]);

  const setVerified = useCallback((verified: boolean) => {
    try {
      stateManager.setVerified(verified);
    } catch (err) {
      setError('Erro ao definir status de verificação');
      console.error('Erro ao definir verificação:', err);
    }
  }, [stateManager]);

  const reset = useCallback(() => {
    try {
      stateManager.reset();
      setError(null);
    } catch (err) {
      setError('Erro ao resetar progresso');
      console.error('Erro ao resetar:', err);
    }
  }, [stateManager]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    progress,
    steps,
    isLoading,
    error,
    completeStep,
    setAffiliateLink,
    setEmail,
    setVerified,
    reset,
    clearError
  };
};
