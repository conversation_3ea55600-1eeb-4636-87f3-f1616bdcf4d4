/**
 * Sistema de Analytics e Tracking
 */

interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp?: number;
  userId?: string;
  sessionId?: string;
}

class AnalyticsManager {
  private static instance: AnalyticsManager;
  private events: AnalyticsEvent[] = [];
  private sessionId: string;
  private userId?: string;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeTracking();
  }

  public static getInstance(): AnalyticsManager {
    if (!AnalyticsManager.instance) {
      AnalyticsManager.instance = new AnalyticsManager();
    }
    return AnalyticsManager.instance;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  private initializeTracking(): void {
    // Track page load
    this.track('page_load', {
      url: window.location.href,
      referrer: document.referrer,
      userAgent: navigator.userAgent.substring(0, 100),
      timestamp: Date.now()
    });

    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      this.track('page_visibility_change', {
        hidden: document.hidden
      });
    });

    // Track page unload
    window.addEventListener('beforeunload', () => {
      this.track('page_unload');
      this.flush();
    });
  }

  public track(eventName: string, properties?: Record<string, any>): void {
    const event: AnalyticsEvent = {
      name: eventName,
      properties: {
        ...properties,
        url: window.location.href,
        timestamp: Date.now()
      },
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId
    };

    this.events.push(event);

    // Auto-flush a cada 10 eventos ou 30 segundos
    if (this.events.length >= 10) {
      this.flush();
    }

    // Log para desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Analytics Event:', eventName, properties);
    }
  }

  public setUserId(userId: string): void {
    this.userId = userId;
    this.track('user_identified', { userId });
  }

  public flush(): void {
    if (this.events.length === 0) return;

    // Simula envio para servidor de analytics
    this.sendToAnalytics(this.events);
    this.events = [];
  }

  private sendToAnalytics(events: AnalyticsEvent[]): void {
    // Em produção, enviaria para Google Analytics, Mixpanel, etc.
    if (process.env.NODE_ENV === 'production') {
      // Google Analytics 4
      events.forEach(event => {
        if (typeof gtag !== 'undefined') {
          gtag('event', event.name, event.properties);
        }
      });

      // Facebook Pixel
      events.forEach(event => {
        if (typeof fbq !== 'undefined') {
          fbq('track', event.name, event.properties);
        }
      });
    }

    // Salva localmente para debug
    try {
      const existingEvents = JSON.parse(localStorage.getItem('analytics_events') || '[]');
      const allEvents = [...existingEvents, ...events];
      // Mantém apenas os últimos 100 eventos
      const recentEvents = allEvents.slice(-100);
      localStorage.setItem('analytics_events', JSON.stringify(recentEvents));
    } catch (error) {
      console.warn('Erro ao salvar eventos de analytics:', error);
    }
  }

  // Métodos específicos para eventos da landing page
  public trackStepCompleted(stepId: number, stepName: string): void {
    this.track('step_completed', {
      step_id: stepId,
      step_name: stepName,
      completion_time: Date.now()
    });
  }

  public trackButtonClick(buttonName: string, location: string): void {
    this.track('button_click', {
      button_name: buttonName,
      location: location
    });
  }

  public trackFormSubmission(formName: string, success: boolean): void {
    this.track('form_submission', {
      form_name: formName,
      success: success
    });
  }

  public trackError(errorType: string, errorMessage: string): void {
    this.track('error_occurred', {
      error_type: errorType,
      error_message: errorMessage,
      stack_trace: new Error().stack?.substring(0, 500)
    });
  }

  public trackPerformance(metric: string, value: number): void {
    this.track('performance_metric', {
      metric_name: metric,
      metric_value: value
    });
  }

  // Funnel tracking específico
  public trackFunnelStep(funnelName: string, stepName: string, stepIndex: number): void {
    this.track('funnel_step', {
      funnel_name: funnelName,
      step_name: stepName,
      step_index: stepIndex
    });
  }

  public trackConversion(conversionType: string, value?: number): void {
    this.track('conversion', {
      conversion_type: conversionType,
      conversion_value: value
    });
  }
}

// Instância global
export const analytics = AnalyticsManager.getInstance();

// Funções de conveniência
export const trackEvent = (name: string, properties?: Record<string, any>) => {
  analytics.track(name, properties);
};

export const trackStepCompleted = (stepId: number, stepName: string) => {
  analytics.trackStepCompleted(stepId, stepName);
};

export const trackButtonClick = (buttonName: string, location: string) => {
  analytics.trackButtonClick(buttonName, location);
};

export const trackError = (errorType: string, errorMessage: string) => {
  analytics.trackError(errorType, errorMessage);
};

// Declarações globais para TypeScript
declare global {
  function gtag(...args: any[]): void;
  function fbq(...args: any[]): void;
}

// Auto-flush periódico
setInterval(() => {
  analytics.flush();
}, 30000); // A cada 30 segundos
