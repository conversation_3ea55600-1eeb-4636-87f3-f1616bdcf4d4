/**
 * Testes para LandingStateManager
 */

import LandingStateManager from '../landingState';
import { UserProgress } from '../../types/landing';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as any;

describe('LandingStateManager', () => {
  let stateManager: LandingStateManager;

  beforeEach(() => {
    // Reset localStorage mock
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    
    // Get fresh instance
    stateManager = LandingStateManager.getInstance();
  });

  afterEach(() => {
    stateManager.reset();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = LandingStateManager.getInstance();
      const instance2 = LandingStateManager.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('Initial State', () => {
    it('should initialize with correct default state', () => {
      const progress = stateManager.getProgress();
      expect(progress.currentStep).toBe(0);
      expect(progress.completedSteps).toEqual([]);
      expect(progress.affiliateLink).toBe('');
      expect(progress.isVerified).toBe(false);
    });

    it('should initialize steps correctly', () => {
      const steps = stateManager.getSteps();
      expect(steps).toHaveLength(5);
      expect(steps[0].active).toBe(true);
      expect(steps[0].completed).toBe(false);
    });
  });

  describe('Step Completion', () => {
    it('should complete step correctly', () => {
      stateManager.completeStep(0);
      const progress = stateManager.getProgress();
      
      expect(progress.completedSteps).toContain(0);
      expect(progress.currentStep).toBe(1);
    });

    it('should not duplicate completed steps', () => {
      stateManager.completeStep(0);
      stateManager.completeStep(0);
      const progress = stateManager.getProgress();
      
      expect(progress.completedSteps.filter(step => step === 0)).toHaveLength(1);
    });

    it('should not exceed maximum step', () => {
      stateManager.completeStep(10);
      const progress = stateManager.getProgress();
      
      expect(progress.currentStep).toBe(4);
    });
  });

  describe('Data Persistence', () => {
    it('should save progress to localStorage', () => {
      stateManager.completeStep(0);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'landing_progress',
        expect.any(String)
      );
    });

    it('should handle localStorage errors gracefully', () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage full');
      });

      expect(() => stateManager.completeStep(0)).not.toThrow();
    });
  });

  describe('Affiliate Link', () => {
    it('should set affiliate link correctly', () => {
      const testLink = 'https://example.com/ref/123';
      stateManager.setAffiliateLink(testLink);
      
      const progress = stateManager.getProgress();
      expect(progress.affiliateLink).toBe(testLink);
    });
  });

  describe('Email Handling', () => {
    it('should set email correctly', () => {
      const testEmail = '<EMAIL>';
      stateManager.setEmail(testEmail);
      
      const progress = stateManager.getProgress();
      expect(progress.email).toBe(testEmail);
    });
  });

  describe('Verification Status', () => {
    it('should set verification status correctly', () => {
      stateManager.setVerified(true);
      
      const progress = stateManager.getProgress();
      expect(progress.isVerified).toBe(true);
    });
  });

  describe('Reset Functionality', () => {
    it('should reset to initial state', () => {
      // Make some changes
      stateManager.completeStep(0);
      stateManager.setAffiliateLink('test-link');
      stateManager.setVerified(true);
      
      // Reset
      stateManager.reset();
      
      const progress = stateManager.getProgress();
      expect(progress.currentStep).toBe(0);
      expect(progress.completedSteps).toEqual([]);
      expect(progress.affiliateLink).toBe('');
      expect(progress.isVerified).toBe(false);
    });
  });

  describe('Observer Pattern', () => {
    it('should notify subscribers on state change', () => {
      const mockCallback = jest.fn();
      const unsubscribe = stateManager.subscribe(mockCallback);
      
      stateManager.completeStep(0);
      
      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          currentStep: 1,
          completedSteps: [0]
        })
      );
      
      unsubscribe();
    });

    it('should allow unsubscribing', () => {
      const mockCallback = jest.fn();
      const unsubscribe = stateManager.subscribe(mockCallback);
      
      unsubscribe();
      stateManager.completeStep(0);
      
      expect(mockCallback).not.toHaveBeenCalled();
    });
  });
});
