# 🚀 Guia de Deploy - AffiliateFlow Pro

## 📋 Pré-requisitos

- Node.js 18+ 
- npm ou yarn
- Git configurado
- Conta no provedor de hospedagem escolhido

## 🔧 Preparação para Deploy

### 1. Validação Pré-Deploy
```bash
# Executar todos os testes e validações
npm run validate

# Verificar build de produção
npm run build

# Analisar bundle (opcional)
npm run build:analyze
```

### 2. Configuração de Ambiente

Crie um arquivo `.env.production`:
```env
VITE_APP_TITLE=AffiliateFlow Pro
VITE_API_URL=https://api.affiliateflow.pro
VITE_GA_ID=G-XXXXXXXXXX
VITE_FB_PIXEL_ID=123456789
VITE_HOTJAR_ID=123456
```

## 🌐 Opções de Deploy

### 1. Vercel (Recomendado)

**Deploy Automático:**
1. Conecte seu repositório no [Vercel](https://vercel.com)
2. Configure as variáveis de ambiente
3. Deploy automático a cada push

**Deploy Manual:**
```bash
npm install -g vercel
vercel --prod
```

**Configuração (vercel.json):**
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    { "source": "/(.*)", "destination": "/index.html" }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

### 2. Netlify

**Deploy via Git:**
1. Conecte repositório no [Netlify](https://netlify.com)
2. Build command: `npm run build`
3. Publish directory: `dist`

**Deploy Manual:**
```bash
npm install -g netlify-cli
npm run build
netlify deploy --prod --dir=dist
```

**Configuração (_redirects):**
```
/*    /index.html   200
```

### 3. GitHub Pages

**Configuração (deploy.yml):**
```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm run test
        
      - name: Build
        run: npm run build
        
      - name: Deploy
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist
```

### 4. AWS S3 + CloudFront

```bash
# Instalar AWS CLI
npm install -g aws-cli

# Configurar credenciais
aws configure

# Build e deploy
npm run build
aws s3 sync dist/ s3://seu-bucket --delete
aws cloudfront create-invalidation --distribution-id XXXXXX --paths "/*"
```

## 🔒 Configurações de Segurança

### Headers de Segurança
```javascript
// Para Vercel/Netlify
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "Strict-Transport-Security",
          "value": "max-age=31536000; includeSubDomains"
        },
        {
          "key": "Content-Security-Policy",
          "value": "default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.affiliateflow.pro"
        }
      ]
    }
  ]
}
```

### Variáveis de Ambiente Seguras
- Nunca commitar chaves de API
- Usar variáveis de ambiente do provedor
- Rotacionar chaves regularmente

## 📊 Monitoramento Pós-Deploy

### 1. Analytics
- Google Analytics 4 configurado
- Facebook Pixel ativo
- Hotjar para heatmaps

### 2. Performance
- Core Web Vitals
- Lighthouse CI
- Bundle size monitoring

### 3. Erros
- Sentry para error tracking
- Console logs em produção desabilitados

## 🔄 CI/CD Pipeline

### GitHub Actions Completo
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run lint
      - run: npm run type-check
      - run: npm run test:coverage
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run build
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: dist
          path: dist/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to production
        run: echo "Deploy to production"
```

## 🚨 Checklist Pré-Deploy

- [ ] Todos os testes passando
- [ ] Build sem erros
- [ ] Variáveis de ambiente configuradas
- [ ] Analytics configurado
- [ ] Headers de segurança definidos
- [ ] Domínio personalizado configurado
- [ ] SSL/HTTPS ativo
- [ ] Redirects configurados
- [ ] Sitemap.xml gerado
- [ ] robots.txt configurado

## 📞 Suporte

Para problemas de deploy:
1. Verificar logs do provedor
2. Testar build local
3. Validar variáveis de ambiente
4. Consultar documentação do provedor
