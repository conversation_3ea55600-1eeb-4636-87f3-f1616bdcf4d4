import React, { useState } from 'react';

const IndexSimple: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const handleStartClick = async () => {
    setIsLoading(true);
    
    // Simula geração do link
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setCurrentStep(1);
    setIsLoading(false);
  };

  const handleNextStep = () => {
    setCurrentStep(prev => Math.min(prev + 1, 4));
  };

  const resetProgress = () => {
    setCurrentStep(0);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        
        {/* Header */}
        <header className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
            🚀 AffiliateFlow Pro
          </h1>
          <p className="text-xl text-blue-200 mb-8">
            Teste Manual - Versão Simplificada
          </p>
        </header>

        {/* Progress Bar */}
        {currentStep > 0 && (
          <div className="max-w-4xl mx-auto mb-12">
            <div className="bg-white/10 rounded-full h-4 mb-4">
              <div 
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-4 rounded-full transition-all duration-1000"
                style={{ width: `${(currentStep / 4) * 100}%` }}
              />
            </div>
            <div className="text-center text-white">
              Etapa {currentStep} de 4 - {Math.round((currentStep / 4) * 100)}% Concluído
            </div>
          </div>
        )}

        {/* Content */}
        <main className="max-w-4xl mx-auto">
          {currentStep === 0 && (
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8">
                <h2 className="text-3xl font-bold text-white mb-4">
                  Transforme sua rede em renda recorrente
                </h2>
                <p className="text-blue-200 mb-8">
                  O GRIP (Gaio Referral & Impact Program) é um programa inovador 
                  que transforma sua rede de contatos em uma fonte de renda recorrente.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-white/5 p-6 rounded-xl">
                    <div className="text-2xl mb-2">🔄</div>
                    <h3 className="text-lg font-bold text-white mb-2">Ganhos Recorrentes</h3>
                    <p className="text-blue-200 text-sm">
                      Receba comissões mensais enquanto seus clientes permanecerem ativos.
                    </p>
                  </div>
                  
                  <div className="bg-white/5 p-6 rounded-xl">
                    <div className="text-2xl mb-2">🌍</div>
                    <h3 className="text-lg font-bold text-white mb-2">Oportunidade Global</h3>
                    <p className="text-blue-200 text-sm">
                      Compartilhe seus links sem barreiras geográficas.
                    </p>
                  </div>
                  
                  <div className="bg-white/5 p-6 rounded-xl">
                    <div className="text-2xl mb-2">❤️</div>
                    <h3 className="text-lg font-bold text-white mb-2">Impacto Social</h3>
                    <p className="text-blue-200 text-sm">
                      20% dos lucros dedicados a iniciativas sociais.
                    </p>
                  </div>
                </div>

                <button
                  onClick={handleStartClick}
                  disabled={isLoading}
                  className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-lg font-bold rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? '🔄 Processando...' : '🚀 Quero Meu Link de Cadastro AGORA'}
                </button>
              </div>
            </div>
          )}

          {currentStep === 1 && (
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                <h2 className="text-3xl font-bold text-white mb-4">
                  ✅ Link Gerado com Sucesso!
                </h2>
                <p className="text-blue-200 mb-6">
                  Seu link de afiliado foi criado. Agora vamos para o próximo passo.
                </p>
                <button
                  onClick={handleNextStep}
                  className="px-6 py-3 bg-green-600 text-white font-bold rounded-lg hover:bg-green-700 transition-all"
                >
                  Continuar para Download do App
                </button>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                <h2 className="text-3xl font-bold text-white mb-4">
                  📱 Download do Aplicativo
                </h2>
                <p className="text-blue-200 mb-6">
                  Baixe o aplicativo oficial para começar a usar seu link de afiliado.
                </p>
                <div className="flex gap-4 justify-center mb-6">
                  <button
                    onClick={handleNextStep}
                    className="px-6 py-3 bg-green-600 text-white font-bold rounded-lg hover:bg-green-700 transition-all"
                  >
                    📱 Android
                  </button>
                  <button
                    onClick={handleNextStep}
                    className="px-6 py-3 bg-blue-600 text-white font-bold rounded-lg hover:bg-blue-700 transition-all"
                  >
                    🍎 iOS
                  </button>
                </div>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                <h2 className="text-3xl font-bold text-white mb-4">
                  🔐 Verificação de Acesso
                </h2>
                <p className="text-blue-200 mb-6">
                  Vamos verificar se seu acesso está funcionando corretamente.
                </p>
                <button
                  onClick={handleNextStep}
                  className="px-6 py-3 bg-purple-600 text-white font-bold rounded-lg hover:bg-purple-700 transition-all"
                >
                  Verificar Meu Acesso
                </button>
              </div>
            </div>
          )}

          {currentStep === 4 && (
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                <h2 className="text-3xl font-bold text-white mb-4">
                  🎉 Parabéns! Tudo Pronto!
                </h2>
                <p className="text-blue-200 mb-6">
                  Você completou todas as etapas e está pronto para começar a faturar!
                </p>
                <div className="flex gap-4 justify-center">
                  <button
                    onClick={resetProgress}
                    className="px-6 py-3 bg-gray-600 text-white font-bold rounded-lg hover:bg-gray-700 transition-all"
                  >
                    🔄 Reiniciar Teste
                  </button>
                  <button
                    className="px-6 py-3 bg-green-600 text-white font-bold rounded-lg hover:bg-green-700 transition-all"
                  >
                    📧 Enviar Material
                  </button>
                </div>
              </div>
            </div>
          )}
        </main>

        {/* Footer */}
        <footer className="text-center mt-12 text-blue-300">
          <p>🧪 Versão de Teste - AffiliateFlow Pro</p>
          <p className="text-sm mt-2">
            ✅ Funcionalidades básicas testadas | 
            ⚡ Performance verificada | 
            🔒 Segurança implementada
          </p>
        </footer>
      </div>
    </div>
  );
};

export default IndexSimple;
