/**
 * Utilitários para SEO e Meta Tags
 */

interface SEOConfig {
  title: string;
  description: string;
  keywords?: string;
  ogImage?: string;
  ogUrl?: string;
  twitterCard?: 'summary' | 'summary_large_image';
}

export const updateSEOTags = (config: SEOConfig) => {
  // Title
  document.title = config.title;
  
  // Meta description
  updateMetaTag('description', config.description);
  
  // Keywords
  if (config.keywords) {
    updateMetaTag('keywords', config.keywords);
  }
  
  // Open Graph
  updateMetaTag('og:title', config.title, 'property');
  updateMetaTag('og:description', config.description, 'property');
  updateMetaTag('og:type', 'website', 'property');
  
  if (config.ogImage) {
    updateMetaTag('og:image', config.ogImage, 'property');
  }
  
  if (config.ogUrl) {
    updateMetaTag('og:url', config.ogUrl, 'property');
  }
  
  // Twitter Card
  updateMetaTag('twitter:card', config.twitterCard || 'summary_large_image');
  updateMetaTag('twitter:title', config.title);
  updateMetaTag('twitter:description', config.description);
  
  if (config.ogImage) {
    updateMetaTag('twitter:image', config.ogImage);
  }
};

const updateMetaTag = (name: string, content: string, attribute: string = 'name') => {
  let element = document.querySelector(`meta[${attribute}="${name}"]`) as HTMLMetaElement;
  
  if (!element) {
    element = document.createElement('meta');
    element.setAttribute(attribute, name);
    document.head.appendChild(element);
  }
  
  element.content = content;
};

// Configuração padrão para a landing page
export const defaultSEOConfig: SEOConfig = {
  title: 'AffiliateFlow Pro - Programa de Afiliados Gaio DataOS | Renda Recorrente',
  description: 'Transforme sua rede de contatos em renda recorrente com o GRIP - Gaio Referral & Impact Program. Comissões de 8% a 12% + impacto social. Cadastre-se agora!',
  keywords: 'programa afiliados, renda recorrente, gaio dataos, grip, comissões, marketing digital, afiliado premium',
  ogImage: '/og-image.jpg',
  ogUrl: window.location.href,
  twitterCard: 'summary_large_image'
};

// Structured Data para SEO
export const addStructuredData = () => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "AffiliateFlow Pro - Programa de Afiliados",
    "description": "Landing page para programa de afiliados com renda recorrente",
    "url": window.location.href,
    "mainEntity": {
      "@type": "Organization",
      "name": "Gaio DataOS",
      "description": "Plataforma de dados com programa de afiliados GRIP",
      "url": "https://gaiodataos.com"
    }
  };
  
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(structuredData);
  document.head.appendChild(script);
};

// Analytics tracking
export const trackEvent = (eventName: string, properties?: Record<string, any>) => {
  // Google Analytics 4
  if (typeof gtag !== 'undefined') {
    gtag('event', eventName, properties);
  }
  
  // Facebook Pixel
  if (typeof fbq !== 'undefined') {
    fbq('track', eventName, properties);
  }
  
  // Console log para desenvolvimento
  if (process.env.NODE_ENV === 'development') {
    console.log('Analytics Event:', eventName, properties);
  }
};

// Performance monitoring
export const reportWebVitals = (metric: any) => {
  // Enviar métricas para analytics
  trackEvent('web_vitals', {
    metric_name: metric.name,
    metric_value: metric.value,
    metric_id: metric.id,
  });
};

declare global {
  function gtag(...args: any[]): void;
  function fbq(...args: any[]): void;
}
