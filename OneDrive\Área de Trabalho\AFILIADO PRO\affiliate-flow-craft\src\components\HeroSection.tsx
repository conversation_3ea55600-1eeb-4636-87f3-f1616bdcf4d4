
import React from 'react';
import { Star, <PERSON>, Zap, TrendingUp, <PERSON>eat, Globe, Heart } from 'lucide-react';

interface HeroSectionProps {
  onStartClick: () => void;
  isLoading: boolean;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onStartClick, isLoading }) => {
  const handleStartClick = () => {
    // Primeiro abre o link de cadastro
    window.open('https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368', '_blank');
    // Depois executa a lógica do fluxo
    onStartClick();
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center px-4 py-20">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-72 h-72 gradient-primary rounded-full opacity-10 blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 gradient-secondary rounded-full opacity-10 blur-3xl animate-float" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] gradient-primary rounded-full opacity-5 blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto text-center">
        {/* Badge */}
        <div className="inline-flex items-center gap-3 glass rounded-full px-6 py-3 mb-8 animate-fade-in-down">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-lp-green rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-lp-light/90">GRIP - Gaio Referral & Impact Program</span>
          </div>
          <div className="w-px h-4 bg-white/20"></div>
          <div className="flex items-center gap-2">
            <Repeat className="w-4 h-4 text-lp-green" />
            <span className="text-sm font-medium text-lp-light/90">Comissões Recorrentes</span>
          </div>
        </div>

        {/* Main Headline */}
        <h1 className="text-hero text-lp-light mb-8 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
          Transforme sua{' '}
          <span className="gradient-text-secondary">rede de contatos</span>{' '}
          em uma fonte de{' '}
          <span className="gradient-text-primary">renda recorrente</span>{' '}
          com o Gaio DataOS
        </h1>

        {/* Subtitle */}
        <p className="text-xl text-lp-light/80 mb-10 max-w-3xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          O GRIP (Gaio Referral & Impact Program) é um programa inovador que transforma sua rede de contatos em uma fonte de renda recorrente, enquanto contribui para causas sociais importantes.
        </p>
        
        {/* Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
          <div className="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-4 mx-auto">
              <Repeat className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-lp-light mb-2">Ganhos Recorrentes</h3>
            <p className="text-white/70">Receba comissões mensais enquanto seus clientes permanecerem ativos. Com a baixa taxa de cancelamento da Gaio, seus ganhos podem durar anos.</p>
          </div>
          
          <div className="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center mb-4 mx-auto">
              <Globe className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-lp-light mb-2">Oportunidade Global</h3>
            <p className="text-white/70">Compartilhe seus links sem barreiras geográficas e expanda seu potencial de ganhos. Com o GRIP, você pode aproveitar oportunidades em qualquer lugar do mundo.</p>
          </div>
          
          <div className="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center mb-4 mx-auto">
              <Heart className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-lp-light mb-2">Impacto Social</h3>
            <p className="text-white/70">20% dos lucros da Gaio são dedicados a iniciativas que transformam a vida de crianças no Brasil, Moçambique e Índia. Ao ganhar, você também ajuda a construir um mundo melhor.</p>
          </div>
        </div>
        <p className="text-subtitle text-lp-light/80 mb-12 max-w-4xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          Dois pilares de comissão para maximizar seus ganhos:
          <span className="flex flex-col md:flex-row justify-center items-center gap-4 mt-4">
            <span className="bg-blue-500/20 text-blue-300 px-4 py-2 rounded-lg font-semibold inline-flex items-center">
              <span className="mr-2 text-xl">🏢</span> 8% a 12% em empresas que você indicar
            </span>
            <span className="bg-green-500/20 text-green-300 px-4 py-2 rounded-lg font-semibold inline-flex items-center">
              <span className="mr-2 text-xl">👥</span> 1% a 3% em clientes indicados pela sua rede
            </span>
          </span>
        </p>

        {/* Social Proof Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
          <div className="glass-dark rounded-2xl p-6 hover-lift">
            <div className="flex items-center justify-center w-12 h-12 gradient-primary rounded-xl mb-4 mx-auto">
              <Users className="w-6 h-6 text-white" />
            </div>
            <div className="text-2xl font-bold text-lp-light mb-2">2.847</div>
            <div className="text-sm text-lp-light/70">Afiliados Ativos</div>
          </div>
          
          <div className="glass-dark rounded-2xl p-6 hover-lift" style={{ animationDelay: '0.1s' }}>
            <div className="flex items-center justify-center w-12 h-12 gradient-secondary rounded-xl mb-4 mx-auto">
              <Star className="w-6 h-6 text-white" />
            </div>
            <div className="text-2xl font-bold text-lp-light mb-2">4.9/5</div>
            <div className="text-sm text-lp-light/70">Avaliação Média</div>
          </div>
          
          <div className="glass-dark rounded-2xl p-6 hover-lift" style={{ animationDelay: '0.2s' }}>
            <div className="flex items-center justify-center w-12 h-12 gradient-primary rounded-xl mb-4 mx-auto">
              <Zap className="w-6 h-6 text-white" />
            </div>
            <div className="text-2xl font-bold text-lp-light mb-2">24h</div>
            <div className="text-sm text-lp-light/70">Aprovação Instantânea</div>
          </div>
        </div>

        {/* Live Activity */}
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
          <div className="flex items-center gap-3 glass rounded-full px-6 py-3">
            <div className="relative">
              <div className="w-3 h-3 bg-lp-green rounded-full"></div>
              <div className="absolute inset-0 w-3 h-3 bg-lp-green rounded-full animate-ping opacity-75"></div>
            </div>
            <span className="text-sm font-medium text-lp-light/90">1.247 pessoas online agora</span>
          </div>
          
          <div className="flex items-center gap-3 glass rounded-full px-6 py-3">
            <div className="relative">
              <div className="w-3 h-3 bg-lp-orange rounded-full"></div>
              <div className="absolute inset-0 w-3 h-3 bg-lp-orange rounded-full animate-ping opacity-75"></div>
            </div>
            <span className="text-sm font-medium text-lp-light/90">Últimos 3 links gerados há 47 seg</span>
          </div>
        </div>

        {/* CTA Button */}
        <div className="animate-fade-in-up" style={{ animationDelay: '1s' }}>
          <button
            onClick={handleStartClick}
            disabled={isLoading}
            aria-label={isLoading ? 'Processando solicitação de link de cadastro' : 'Solicitar link de cadastro para programa de afiliados'}
            aria-describedby="cta-description"
            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-lg font-bold rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-4 focus:ring-blue-500/50"
          >
            {isLoading ? '🔄 Processando...' : '🚀 Quero Meu Link de Cadastro AGORA'}
          </button>
          <p id="cta-description" className="text-lp-light/60 text-sm mt-4">
            Clique acima para acessar o programa e começar a ganhar comissões recorrentes
          </p>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
