/**
 * Utilitários de validação e sanitização
 */

// Validação de email
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

// Validação de URL
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Sanitização de string
export const sanitizeString = (str: string): string => {
  return str
    .trim()
    .replace(/[<>]/g, '') // Remove caracteres perigosos
    .substring(0, 1000); // <PERSON>ita tamanho
};

// Validação de progresso do usuário
export const validateUserProgress = (progress: any): boolean => {
  if (!progress || typeof progress !== 'object') return false;
  
  const requiredFields = ['currentStep', 'completedSteps', 'affiliateLink', 'isVerified'];
  return requiredFields.every(field => field in progress);
};

// Validação de step ID
export const validateStepId = (stepId: any): boolean => {
  return typeof stepId === 'number' && stepId >= 0 && stepId <= 4;
};

// Rate limiting simples
class RateLimiter {
  private attempts: Map<string, number[]> = new Map();
  
  isAllowed(key: string, maxAttempts: number = 5, windowMs: number = 60000): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    
    // Remove tentativas antigas
    const validAttempts = attempts.filter(time => now - time < windowMs);
    
    if (validAttempts.length >= maxAttempts) {
      return false;
    }
    
    validAttempts.push(now);
    this.attempts.set(key, validAttempts);
    return true;
  }
  
  reset(key: string): void {
    this.attempts.delete(key);
  }
}

export const rateLimiter = new RateLimiter();

// Validação de entrada para formulários
export interface FormValidationResult {
  isValid: boolean;
  errors: string[];
}

export const validateForm = (data: Record<string, any>): FormValidationResult => {
  const errors: string[] = [];
  
  // Validação de email se presente
  if (data.email && !validateEmail(data.email)) {
    errors.push('Email inválido');
  }
  
  // Validação de campos obrigatórios
  const requiredFields = ['email'];
  requiredFields.forEach(field => {
    if (data[field] && !data[field].trim()) {
      errors.push(`Campo ${field} é obrigatório`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Escape HTML para prevenir XSS
export const escapeHtml = (text: string): string => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

// Validação de localStorage
export const isLocalStorageAvailable = (): boolean => {
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
};

// Validação de sessão
export const validateSession = (): boolean => {
  try {
    const sessionData = sessionStorage.getItem('affiliate_session');
    if (!sessionData) return true; // Nova sessão é válida
    
    const session = JSON.parse(sessionData);
    const now = Date.now();
    const sessionAge = now - (session.timestamp || 0);
    
    // Sessão expira em 24 horas
    return sessionAge < 24 * 60 * 60 * 1000;
  } catch {
    return false;
  }
};

// Criar nova sessão
export const createSession = (): void => {
  try {
    const sessionData = {
      id: Math.random().toString(36).substring(2),
      timestamp: Date.now(),
      userAgent: navigator.userAgent.substring(0, 100)
    };
    sessionStorage.setItem('affiliate_session', JSON.stringify(sessionData));
  } catch (error) {
    console.warn('Não foi possível criar sessão:', error);
  }
};
